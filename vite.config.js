import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'] // 关闭警告
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    // UniApp 专用配置，避免与 App Plus 平台冲突
    target: 'es2015',
    minify: process.env.NODE_ENV === 'production' ? 'terser' : false,
    sourcemap: false, // 在生产环境中关闭 sourcemap 以提高性能
    rollupOptions: {
      output: {
        // 不设置 format，让 UniApp 自动决定
        manualChunks: undefined, // 禁用手动代码分割
        inlineDynamicImports: true, // 内联动态导入，避免代码分割问题
      }
    },
    // 关闭代码分割以避免 IIFE 格式错误
    chunkSizeWarningLimit: 2000
  },
  server: {
    proxy: {
      // 代理 API 请求
      '/prod-api': {
        target: 'http://163.204.157.139:8082/prod-api',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/prod-api/, '')
      },
      // 代理 AI 相关请求
      '/prod-ai': {
        target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:8443',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/prod-ai/, '')
      },
      // 代理流式聊天请求
      '/stream-api': {
        target: 'https://u461063-b0dd-d6bab371.westc.gpuhub.com:8443',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path.replace(/^\/stream-api/, '')
      }
    }
  }
})
